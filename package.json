{"name": "lynx-app", "version": "0.0.0", "type": "module", "scripts": {"build": "rspeedy build", "dev": "rspeedy dev", "preview": "rspeedy preview", "test": "vitest run"}, "dependencies": {"@lynx-js/react": "^0.111.2", "rsbuild-plugin-tailwindcss": "^0.2.2"}, "devDependencies": {"@lynx-js/qrcode-rsbuild-plugin": "^0.4.0", "@lynx-js/react-rsbuild-plugin": "^0.10.8", "@lynx-js/rspeedy": "^0.10.2", "@lynx-js/tailwind-preset": "^0.1.1", "@lynx-js/types": "3.3.0", "@rsbuild/plugin-type-check": "1.2.3", "@testing-library/jest-dom": "^6.6.3", "@types/react": "^18.3.23", "jsdom": "^26.1.0", "typescript": "~5.8.3", "vitest": "^3.2.4"}, "engines": {"node": ">=18"}}